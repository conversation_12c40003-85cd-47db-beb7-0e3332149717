apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-secret
  labels:
    app: postgres
data:
  POSTGRES_DB: ps_db
  POSTGRES_USER: ps_user
  POSTGRES_PASSWORD: SecurePassword
  
# In Kubernetes, a ConfigMap is an API object that stores configuration data in key-value pairs, which pods or containers can use in a cluster. ConfigMaps helps decouple configuration details from the application code, making it easier to manage and update configuration settings without changing the application’s code.

# Let’s break down the above configuration:

# apiVersion: v1 specifies the Kubernetes API version used for this ConfigMap.

# kind: ConfigMap defines the Kubernetes resource type.

# Under metadata, the name field specifies the name of the ConfigMap, set as “postgres-secret.” Additionally, labels are applied to the ConfigMap to help identify and organize resources.

# The data section contains the configuration data as key-value pairs.

# POSTGRES_DB: Specify the default database name for PostgreSQL.

# POSTGRES_USER: Specify the default username for PostgreSQL.

# POSTGRES_PASSWORD: Specify the default password for the PostgreSQL user.