apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-volume-claim
  labels:
    app: postgres
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi

# kind: PersistentVolumeClaim indicates that this YAML defines a PersistentVolumeClaim resource.

# storageClassName: manual specifies the desired StorageClass for this PersistentVolumeClaim.

# accessModes specifies the access mode required by the PersistentVolumeClaim.

# Resources define the requested resources for the PersistentVolumeClaim:

# The requests section specifies the amount of storage requested.
