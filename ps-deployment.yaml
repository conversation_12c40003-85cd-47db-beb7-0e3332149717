apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 3
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: 'postgres:14'
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5432
          envFrom:
            - configMapRef:
                name: postgres-secret
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: postgresdata
      volumes:
        - name: postgresdata
          persistentVolumeClaim:
            claimName: postgres-volume-claim

# replicas: 3 specifies the desired number of replicas.

# selector specifies how the Deployment identifies which Pods it manages.

# template defines the Pod template used for creating new Pods controlled by this Deployment. Under metadata, the labels field assigns labels to the Pods created from this template, with app: postgres.

# containers specify the containers within the Pod.

# name: postgres is the name assigned to the container.

# image: postgres:14 specifies the Docker image for the PostgreSQL database.

# imagePullPolicy: “IfNotPresent” specifies the policy for pulling the container image.

# ports specify the ports that the container exposes.

# envFrom allows the container to load environment variables from a ConfigMap.

# volumeMounts allows mounting volumes into the container.

# volumes define the volumes that can be mounted into the Pod.

# name: postgresdata specifies the name of the volume.

# persistentVolumeClaim refers to a PersistentVolumeClaim named “postgres-volume-claim”. This claim is likely used to provide persistent storage to the PostgreSQL container so that data is retained across Pod restarts or rescheduling.