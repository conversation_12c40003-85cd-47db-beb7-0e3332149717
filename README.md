# Kubernetes PostgreSQL Setup

This repository contains Kubernetes configurations and management commands for running PostgreSQL in a Kubernetes cluster.

## Prerequisites

- Kubernetes cluster (local or cloud)
- kubectl configured and connected to your cluster
- PostgreSQL pod running with the name pattern `postgres-*`

## Configuration Management

### View ConfigMaps
```bash
kubectl get configmap
```

## Database Operations

### Backup Database

1. **Create a backup file from PostgreSQL pod:**
   ```bash
   kubectl exec -it postgres-665b7554dc-cddgq -- pg_dump -U ps_user -d ps_db > db_backup.sql
   ```

2. **Copy backup file to pod (if needed):**
   ```bash
   kubectl cp db_backup.sql postgres-665b7554dc-cddgq:/tmp/db_backup.sql
   ```

### Restore Database

1. **Connect to PostgreSQL pod:**
   ```bash
   kubectl exec -it postgres-665b7554dc-cddgq -- /bin/bash
   ```

2. **Restore data from backup:**
   ```bash
   psql -U ps_user -d ps_db -f /tmp/db_backup.sql
   ```

## Common Commands

### Pod Management
```bash
# List all pods
kubectl get pods

# Get pod details
kubectl describe pod postgres-665b7554dc-cddgq

# View pod logs
kubectl logs postgres-665b7554dc-cddgq
```

### Database Connection
```bash
# Connect to PostgreSQL directly
kubectl exec -it postgres-665b7554dc-cddgq -- psql -U ps_user -d ps_db
```

## Exposing PostgreSQL with ngrok and Connecting via DBeaver

### Expose PostgreSQL with ngrok (TCP)

1. **Start ngrok with a TCP tunnel:**
   ```bash
   ngrok tcp <local_postgres_port>
   # Example if your PostgreSQL is on port 63327:
   ngrok tcp 63327
   ```
   - ngrok will display a forwarding address like:
     ```
     tcp://0.tcp.ap.ngrok.io:13178 -> localhost:63327
     ```

2. **Note the public host and port** from the ngrok output (e.g., `0.tcp.ap.ngrok.io` and `13178`).

### Connect to PostgreSQL using DBeaver

1. Open DBeaver and create a new PostgreSQL connection.
2. Use the following settings:
   - **Host:** `0.tcp.ap.ngrok.io` (from ngrok)
   - **Port:** `13178` (from ngrok)
   - **Database:** `ps_db`
   - **Username:** `ps_user`
   - **Password:** `SecurePassword`
3. Click **Test Connection**. If successful, click **Finish**.

#### Troubleshooting
- Ensure your PostgreSQL server is listening on all interfaces (`0.0.0.0`), not just `localhost`.
- If you get connection errors, check your Kubernetes service and firewall settings.
- If you see authentication errors, double-check the username and password.
- The ngrok tunnel must be started with `tcp`, not `http`.

## Notes

- Replace `postgres-665b7554dc-cddgq` with your actual PostgreSQL pod name
- Ensure the database user (`ps_user`) has appropriate permissions
- Always test restore operations in a non-production environment first

